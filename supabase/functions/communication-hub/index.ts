import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface CommunicationRequest {
  type: 'send_campaign' | 'get_analytics' | 'create_smart_group' | 'send_push';
  data: any;
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log("🎯 Communication Hub called");
    
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    const { type, data }: CommunicationRequest = await req.json();
    console.log(`📋 Processing request type: ${type}`);

    switch (type) {
      case 'send_campaign':
        return await handleSendCampaign(supabase, data);
      
      case 'get_analytics':
        return await handleGetAnalytics(supabase, data);
      
      case 'create_smart_group':
        return await handleCreateSmartGroup(supabase, data);
      
      case 'send_push':
        return await handleSendPush(supabase, data);
      
      default:
        throw new Error(`Unknown request type: ${type}`);
    }

  } catch (error: any) {
    console.error("💥 Error in communication hub:", error);
    return new Response(
      JSON.stringify({ 
        error: error.message,
        success: false 
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );
  }
};

async function handleSendCampaign(supabase: any, data: any) {
  const { campaignId, clubId } = data;
  console.log(`📧 Sending campaign ${campaignId}`);

  // Get campaign details
  const { data: campaign, error: campaignError } = await supabase
    .from('communication_campaigns')
    .select('*')
    .eq('id', campaignId)
    .single();

  if (campaignError) {
    throw new Error(`Campaign not found: ${campaignError.message}`);
  }

  // Get recipients based on target groups
  const recipients = await getRecipientsForGroups(supabase, campaign.target_groups, clubId);

  // Process each channel
  const results = [];
  
  for (const channel of campaign.channels) {
    switch (channel) {
      case 'email':
        // Call email campaign function
        const emailResult = await supabase.functions.invoke('send-email-campaign', {
          body: {
            campaignId,
            recipients: recipients.map(r => r.email),
            subject: campaign.title,
            content: campaign.message_content,
            clubId
          }
        });
        results.push({ channel: 'email', result: emailResult });
        break;

      case 'push':
        // Send push notifications directly to dashboard
        await sendPushNotifications(supabase, recipients, campaign, clubId);
        results.push({ channel: 'push', success: true });
        break;

      case 'dashboard':
        // Create dashboard message
        await createDashboardMessage(supabase, campaign, clubId);
        results.push({ channel: 'dashboard', success: true });
        break;
    }
  }

  // Update campaign status
  await supabase
    .from('communication_campaigns')
    .update({
      status: 'sent',
      sent_at: new Date().toISOString()
    })
    .eq('id', campaignId);

  return new Response(JSON.stringify({
    success: true,
    message: 'Campaign sent successfully',
    results
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' },
  });
}

async function handleGetAnalytics(supabase: any, data: any) {
  const { clubId, timeframe } = data;
  console.log(`📊 Getting analytics for club ${clubId}`);

  const startDate = new Date();
  switch (timeframe) {
    case 'week':
      startDate.setDate(startDate.getDate() - 7);
      break;
    case 'month':
      startDate.setMonth(startDate.getMonth() - 1);
      break;
    case 'year':
      startDate.setFullYear(startDate.getFullYear() - 1);
      break;
    default:
      startDate.setDate(startDate.getDate() - 30);
  }

  const { data: analytics, error } = await supabase
    .from('communication_analytics')
    .select('*')
    .eq('club_id', clubId)
    .gte('created_at', startDate.toISOString());

  if (error) {
    throw new Error(`Analytics error: ${error.message}`);
  }

  // Process analytics data
  const processed = processAnalyticsData(analytics);

  return new Response(JSON.stringify({
    success: true,
    analytics: processed
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' },
  });
}

async function handleCreateSmartGroup(supabase: any, data: any) {
  const { name, criteria, clubId } = data;
  console.log(`👥 Creating smart group: ${name}`);

  // Create the group
  const { data: group, error } = await supabase
    .from('communication_groups')
    .insert({
      name,
      criteria,
      club_id: clubId,
      is_dynamic: true
    })
    .select()
    .single();

  if (error) {
    throw new Error(`Group creation error: ${error.message}`);
  }

  return new Response(JSON.stringify({
    success: true,
    group
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' },
  });
}

async function handleSendPush(supabase: any, data: any) {
  const { recipients, title, content, clubId } = data;
  console.log(`🔔 Sending push notifications to ${recipients.length} recipients`);

  await sendPushNotifications(supabase, recipients, { title, message_content: content }, clubId);

  return new Response(JSON.stringify({
    success: true,
    message: 'Push notifications sent'
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' },
  });
}

async function getRecipientsForGroups(supabase: any, targetGroups: string[], clubId: string) {
  console.log(`🎯 Getting recipients for groups: ${targetGroups.join(', ')}`);
  
  let query = supabase
    .from('club_memberships')
    .select('user_id, email, first_name, last_name')
    .eq('club_id', clubId)
    .eq('is_active', true);

  // Apply group filters based on target groups
  // This is a simplified version - you could make it more sophisticated
  
  const { data: members, error } = await query;
  
  if (error) {
    throw new Error(`Recipients error: ${error.message}`);
  }

  return members;
}

async function sendPushNotifications(supabase: any, recipients: any[], campaign: any, clubId: string) {
  console.log(`🔔 Creating push notifications for ${recipients.length} recipients`);

  // Create dashboard messages for each recipient
  const messages = recipients.map(recipient => ({
    club_id: clubId,
    recipient_type: 'individual',
    recipient_ids: [recipient.user_id],
    title: campaign.title,
    content: campaign.message_content,
    channel: 'push',
    type: 'notification',
    priority: 'normal',
    expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
  }));

  const { error } = await supabase
    .from('communication_messages')
    .insert(messages);

  if (error) {
    throw new Error(`Push notification error: ${error.message}`);
  }
}

async function createDashboardMessage(supabase: any, campaign: any, clubId: string) {
  console.log(`📰 Creating dashboard message`);

  const { error } = await supabase
    .from('communication_messages')
    .insert({
      club_id: clubId,
      recipient_type: 'all',
      title: campaign.title,
      content: campaign.message_content,
      channel: 'dashboard',
      type: 'news',
      priority: 'normal'
    });

  if (error) {
    throw new Error(`Dashboard message error: ${error.message}`);
  }
}

function processAnalyticsData(analytics: any[]) {
  const stats = {
    total_campaigns: 0,
    total_emails_sent: 0,
    total_push_sent: 0,
    avg_engagement: 0,
    by_channel: {} as any
  };

  analytics.forEach(entry => {
    if (entry.event_type === 'campaign_sent') {
      stats.total_campaigns++;
      
      if (entry.channel === 'email') {
        stats.total_emails_sent += entry.metadata?.recipients_count || 0;
      } else if (entry.channel === 'push') {
        stats.total_push_sent += entry.metadata?.recipients_count || 0;
      }
      
      if (!stats.by_channel[entry.channel]) {
        stats.by_channel[entry.channel] = { sent: 0, opened: 0, clicked: 0 };
      }
      stats.by_channel[entry.channel].sent += entry.metadata?.recipients_count || 0;
    }
  });

  return stats;
}

serve(handler);