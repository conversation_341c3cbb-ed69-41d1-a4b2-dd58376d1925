import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.53.0';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface EmailRequest {
  clubId: string;
  to: string | string[];
  subject: string;
  html?: string;
  text?: string;
  from?: string;
  replyTo?: string;
}

interface SmtpConfig {
  enabled: boolean;
  host: string;
  port: number;
  secure: boolean;
  user: string;
  password: string;
  from_name: string;
  from_email: string;
  reply_to?: string;
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { clubId, to, subject, html, text, from, replyTo }: EmailRequest = await req.json();

    // Initialize Supabase client
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get club SMTP configuration
    const { data: club, error: clubError } = await supabase
      .from('clubs')
      .select('settings, name')
      .eq('id', clubId)
      .single();

    if (clubError || !club) {
      throw new Error(`Club not found: ${clubError?.message}`);
    }

    const smtpConfig = club.settings?.smtp;

    // SMTP must be enabled and configured
    if (!smtpConfig?.enabled || !smtpConfig.host || !smtpConfig.user) {
      throw new Error('SMTP ist nicht konfiguriert oder deaktiviert. Bitte konfiguriere SMTP in den Club-Einstellungen.');
    }

    // Send via SMTP
    return await sendViaSmtp(smtpConfig, {
      to,
      subject,
      html,
      text,
      from: from || `${smtpConfig.from_name} <${smtpConfig.from_email}>`,
      replyTo: replyTo || smtpConfig.reply_to
    });

  } catch (error: any) {
    console.error('Error in club-email-service:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );
  }
};

async function sendViaSmtp(smtpConfig: SmtpConfig, emailData: any): Promise<Response> {
  try {
    console.log('Connecting to SMTP server:', {
      host: smtpConfig.host,
      port: smtpConfig.port,
      secure: smtpConfig.secure,
      user: smtpConfig.user
    });

    // For Gmail, handle TLS properly
    const isGmail = smtpConfig.host.includes('gmail');
    
    // Create TCP connection
    const conn = await Deno.connect({
      hostname: smtpConfig.host,
      port: smtpConfig.port,
    });

    const encoder = new TextEncoder();
    const decoder = new TextDecoder();

    // Helper functions
    const readResponse = async (): Promise<string> => {
      const buffer = new Uint8Array(1024);
      const n = await conn.read(buffer);
      if (!n) throw new Error('Connection closed');
      return decoder.decode(buffer.subarray(0, n));
    };

    const sendCommand = async (command: string): Promise<string> => {
      await conn.write(encoder.encode(command + '\r\n'));
      return await readResponse();
    };

    try {
      // 1. Read server greeting
      const greeting = await readResponse();
      console.log('SMTP Greeting:', greeting);
      if (!greeting.startsWith('220')) {
        throw new Error(`Invalid greeting: ${greeting}`);
      }

      // 2. Send EHLO
      const ehloResponse = await sendCommand(`EHLO ${smtpConfig.host}`);
      console.log('EHLO Response:', ehloResponse);
      if (!ehloResponse.startsWith('250')) {
        throw new Error(`EHLO failed: ${ehloResponse}`);
      }

      // 3. Handle Gmail authentication directly (skip STARTTLS due to Edge Function limitations)
      if (isGmail && smtpConfig.port === 587) {
        console.log('Gmail detected - attempting direct authentication');
        // Note: For Gmail on port 587, we would need STARTTLS but Edge Functions can't upgrade
        // This is a limitation that needs to be documented
      }

      // 4. Authenticate
      const authResponse = await sendCommand('AUTH LOGIN');
      console.log('AUTH Response:', authResponse);
      if (!authResponse.startsWith('334')) {
        if (isGmail && authResponse.includes('530')) {
          throw new Error(`Gmail erfordert App-spezifisches Passwort. Bitte erstellen Sie ein App-Passwort in Ihren Google-Kontoeinstellungen.`);
        }
        throw new Error(`AUTH LOGIN failed: ${authResponse}`);
      }

      // Send username (base64 encoded)
      const usernameB64 = btoa(smtpConfig.user);
      const userResponse = await sendCommand(usernameB64);
      console.log('Username Response:', userResponse);
      if (!userResponse.startsWith('334')) {
        throw new Error(`Username rejected: ${userResponse}`);
      }

      // Send password (base64 encoded)
      const passwordB64 = btoa(smtpConfig.password);
      const passResponse = await sendCommand(passwordB64);
      console.log('Password Response:', passResponse);
      if (!passResponse.startsWith('235')) {
        if (isGmail) {
          throw new Error(`Gmail-Authentifizierung fehlgeschlagen. Stellen Sie sicher, dass Sie ein App-spezifisches Passwort verwenden: ${passResponse}`);
        }
        throw new Error(`Authentication failed: ${passResponse}`);
      }

      // 5. Send email
      // MAIL FROM
      const mailFromResponse = await sendCommand(`MAIL FROM:<${smtpConfig.from_email}>`);
      if (!mailFromResponse.startsWith('250')) {
        throw new Error(`MAIL FROM failed: ${mailFromResponse}`);
      }

      // RCPT TO (handle both string and array)
      const recipients = Array.isArray(emailData.to) ? emailData.to : [emailData.to];
      for (const recipient of recipients) {
        const rcptResponse = await sendCommand(`RCPT TO:<${recipient}>`);
        if (!rcptResponse.startsWith('250')) {
          throw new Error(`RCPT TO failed for ${recipient}: ${rcptResponse}`);
        }
      }

      // DATA
      const dataResponse = await sendCommand('DATA');
      if (!dataResponse.startsWith('354')) {
        throw new Error(`DATA command failed: ${dataResponse}`);
      }

      // Email content
      const emailContent = [
        `From: ${emailData.from}`,
        `To: ${recipients.join(', ')}`,
        `Subject: ${emailData.subject}`,
        emailData.replyTo ? `Reply-To: ${emailData.replyTo}` : '',
        'MIME-Version: 1.0',
        'Content-Type: text/html; charset=UTF-8',
        '',
        emailData.html || emailData.text || '',
        '.',
      ].filter(line => line !== '').join('\r\n');

      const sendResponse = await sendCommand(emailContent);
      if (!sendResponse.startsWith('250')) {
        throw new Error(`Email sending failed: ${sendResponse}`);
      }

      // QUIT
      await sendCommand('QUIT');

      console.log('Email sent successfully via SMTP');

      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'E-Mail erfolgreich über SMTP gesendet',
          provider: 'smtp',
          host: smtpConfig.host,
          recipients: recipients.length
        }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );

    } finally {
      conn.close();
    }

  } catch (error: any) {
    console.error('SMTP sending failed:', error);
    
    // Provide Gmail-specific error messages
    if (error.message?.includes('530') || error.message?.includes('STARTTLS') || error.message?.includes('Gmail')) {
      throw new Error(`Gmail SMTP-Fehler: Für Gmail ist ein App-spezifisches Passwort erforderlich. 
      
Bitte:
1. Gehen Sie zu https://myaccount.google.com/apppasswords
2. Erstellen Sie ein App-Passwort für "E-Mail"
3. Verwenden Sie dieses Passwort in den SMTP-Einstellungen
4. Stellen Sie sicher, dass 2-Faktor-Authentifizierung aktiviert ist

Ursprünglicher Fehler: ${error.message}`);
    }
    
    throw new Error(`SMTP-Versendung fehlgeschlagen: ${error.message}`);
  }
}

serve(handler);